%% ========================================================================
%  三维偶极子声波测井有限差分时域（FDTD）正演模拟程序
%  3D Dipole Acoustic Logging Finite-Difference Time-Domain Forward Modeling
%% ========================================================================
clc;clear;close all;

%% ========================================================================
%  快速设置区域 - 用户主要调整参数
%% ========================================================================

% *** 时间步长快速设置 ***
CUSTOM_TIME_STEPS = 10000;   % 偶极子需要更长时间捕获横波
                            % 5000步  ≈ 0.5-1.0毫秒
                            % 8000步  ≈ 0.8-1.6毫秒 (推荐)
                            % 10000步 ≈ 1.0-2.0毫秒 (高精度)

% *** 计算精度快速设置 ***
CFL_NUMBER = 0.3;          % CFL数：0.2(高精度慢), 0.3(标准), 0.4(快速)

% *** 震源频率快速设置 ***
SOURCE_FREQUENCY = 3000;   % 偶极子震源主频率(Hz)：2000, 3000, 4000

% *** 进度显示设置 ***
PROGRESS_INTERVAL = 200;   % 进度显示间隔：每多少步显示一次进度

fprintf('=== 偶极子声波测井快速设置 ===\n');
fprintf('自定义时间步数: %d\n', CUSTOM_TIME_STEPS);
fprintf('CFL数: %.1f\n', CFL_NUMBER);
fprintf('震源频率: %.0f Hz\n', SOURCE_FREQUENCY);
fprintf('进度显示间隔: 每%d步\n', PROGRESS_INTERVAL);
fprintf('预计记录时间: %.1f-%.1f 毫秒\n', CUSTOM_TIME_STEPS*0.1e-3, CUSTOM_TIME_STEPS*0.2e-3);
fprintf('==============================\n\n');

%% ========================================================================
%  第一部分：初始化设置
%% ========================================================================

% 震源频率参数（需要先定义用于网格设计）
f0 = SOURCE_FREQUENCY;  % 主频率 Hz (从快速设置区域获取)

%% ========================================================================
%  物理模型参数定义
%% ========================================================================

% 井内流体参数（钻井液/泥浆）
vp_fluid = 1500;   % 纵波速度 (m/s)
vs_fluid = 0;      % 横波速度 (m/s) - 流体不传横波
rho_fluid = 1000;  % 密度 (kg/m³)

% 井外地层参数（快速地层 - 适合偶极子测井）
vp_rock = 4000;    % 纵波速度 (m/s)
vs_rock = 2300;    % 横波速度 (m/s) - 偶极子主要激发横波
rho_rock = 2500;   % 密度 (kg/m³)

% 网格设计用的关键参数
vs_min = vs_rock;  % 横波速度决定最小波长（偶极子主要关注横波）
vp_max = max(vp_fluid, vp_rock);  % 最大速度用于CFL条件

% 波长计算（基于横波，因为偶极子主要激发横波）
lambda_min = vs_min / f0;  % 最小波长 = 2300/3000 = 0.767 m
fprintf('最小波长(横波): %.4f m\n', lambda_min);

% 网格间距设计（每波长至少10-15个网格点，推荐15-25个用于更好的频散控制）
points_per_wavelength = 20;  % 每波长网格点数（偶极子需要较密网格控制横波频散）
dx = lambda_min / points_per_wavelength;  % ≈ 0.038 m
dy = dx;  % 保持各向同性
dz = dx;  % 保持各向同性

fprintf('网格间距: dx=dy=dz=%.4f m\n', dx);
fprintf('每波长网格点数: %.1f个\n', points_per_wavelength);

% 物理区域尺寸
Lx = 1.0;
Ly = 1.0;
Lz = 6.0;

% 根据网格间距计算网格数量
nx = round(Lx/dx) + 1;
ny = round(Ly/dy) + 1;
nz = round(Lz/dz) + 1;

% 调整物理尺寸以匹配网格
Lx = (nx-1) * dx;
Ly = (ny-1) * dy;
Lz = (nz-1) * dz;

fprintf('实际网格数量: nx=%d, ny=%d, nz=%d\n', nx, ny, nz);
fprintf('实际物理尺寸: Lx=%.3f, Ly=%.3f, Lz=%.3f m\n', Lx, Ly, Lz);

% 时间步数设置
nt = CUSTOM_TIME_STEPS;  % 从快速设置区域获取

% 流体弹性模量
G_fluid = rho_fluid * vs_fluid^2;
K_fluid = rho_fluid * vp_fluid^2 - 4*G_fluid/3;

% 岩石弹性模量
G_rock = rho_rock * vs_rock^2;
K_rock = rho_rock * vp_rock^2 - 4*G_rock/3;

% 井筒几何参数
well_radius = 0.1;
well_center_x = Lx / 2;
well_center_y = Ly / 2;

% 材料参数数组初始化
K = zeros(nx, ny, nz);
G = zeros(nx, ny, nz);
rho = zeros(nx, ny, nz);

% 材料参数空间分配（井筒内外区分）
for i = 1:nx
    for j = 1:ny
        x = (i-1) * dx;
        y = (j-1) * dy;
        
        distance_to_well = sqrt((x - well_center_x)^2 + (y - well_center_y)^2);
        
        if distance_to_well <= well_radius
            K(i, j, :) = K_fluid;
            G(i, j, :) = G_fluid;
            rho(i, j, :) = rho_fluid;
        else
            K(i, j, :) = K_rock;
            G(i, j, :) = G_rock;
            rho(i, j, :) = rho_rock;
        end
    end
end

% ========================================================================
% 改进的CFL稳定性条件和时间步长
% ========================================================================

% CFL条件设置（平衡精度和计算效率）
cfl = CFL_NUMBER;  % 从快速设置区域获取CFL数

% 3D情况下的CFL条件
dt = cfl * min([dx, dy, dz]) / (vp_max * sqrt(3));  % 3D修正

% 频率相关的时间步长检查
dt_freq = 1 / (20 * f0);  % 每个周期至少20个时间步
dt = min(dt, dt_freq);    % 取更严格的条件

fprintf('CFL数: %.2f\n', cfl);
fprintf('时间步长: %.2e s\n', dt);
fprintf('每个周期时间步数: %.1f\n', 1/(f0*dt));

% 记录时间长度
record_time = nt * dt;

% ========================================================================
% 详细的时间信息显示
% ========================================================================
fprintf('\n=== 偶极子声波测井时间参数 ===\n');
fprintf('总时间步数: %d (用户设置)\n', nt);
fprintf('时间步长: %.3e 秒 (%.3f 微秒) (由CFL数自动计算)\n', dt, dt*1e6);
fprintf('总记录时间: %.3e 秒 (%.1f 毫秒) (= 步数 × 步长)\n', record_time, record_time*1e3);
fprintf('震源主频率: %.0f Hz (周期: %.1f 微秒)\n', f0, 1e6/f0);
fprintf('总记录包含: %.1f 个震源周期\n', record_time * f0);

% 估算波传播距离
max_distance_p = record_time * vp_rock;  % 纵波最大传播距离
max_distance_s = record_time * vs_rock;  % 横波最大传播距离
fprintf('纵波最大传播距离: %.2f 米\n', max_distance_p);
fprintf('横波最大传播距离: %.2f 米 (偶极子主要关注)\n', max_distance_s);
fprintf('相当于井筒长度的 %.1f 倍(纵波), %.1f 倍(横波)\n', max_distance_p/Lz, max_distance_s/Lz);

% ========================================================================
% 数值频散分析和参数验证
% ========================================================================

fprintf('\n=== 偶极子声波测井物理模型验证 ===\n');
fprintf('井筒内流体（钻井液）：\n');
fprintf('  纵波速度：%.0f m/s\n', vp_fluid);
fprintf('  横波速度：%.0f m/s (流体不传横波)\n', vs_fluid);
fprintf('  密度：%.0f kg/m³\n', rho_fluid);

fprintf('井外地层（快速地层）：\n');
fprintf('  纵波速度：%.0f m/s\n', vp_rock);
fprintf('  横波速度：%.0f m/s (偶极子主要激发)\n', vs_rock);
fprintf('  密度：%.0f kg/m³\n', rho_rock);
fprintf('  泊松比：%.3f\n', (vp_rock^2 - 2*vs_rock^2)/(2*(vp_rock^2 - vs_rock^2)));

fprintf('井筒几何：\n');
fprintf('  井筒半径：%.1f m\n', well_radius);
fprintf('  井筒中心：(%.1f, %.1f) m\n', well_center_x, well_center_y);

% 验证物理参数的合理性
fprintf('\n=== 物理参数验证 ===\n');
if vs_fluid == 0
    fprintf('✓ 流体横波速度为0，符合物理规律\n');
else
    fprintf('❌ 警告：流体横波速度应为0\n');
end

if vp_rock > vp_fluid && vs_rock > 0
    fprintf('✓ 岩石波速大于流体波速，符合常规地层\n');
else
    fprintf('❌ 警告：岩石波速参数可能不合理\n');
end

if rho_rock > rho_fluid
    fprintf('✓ 岩石密度大于流体密度，符合物理规律\n');
else
    fprintf('❌ 警告：密度关系可能不合理\n');
end

% 计算阻抗对比
impedance_fluid = rho_fluid * vp_fluid;
impedance_rock_p = rho_rock * vp_rock;
impedance_rock_s = rho_rock * vs_rock;
fprintf('纵波阻抗对比：%.3f\n', (impedance_rock_p - impedance_fluid) / (impedance_rock_p + impedance_fluid));
fprintf('横波阻抗：%.1e (偶极子主要关注)\n', impedance_rock_s);

fprintf('\n=== 数值频散控制参数 ===\n');
fprintf('主频率：%.0f Hz\n', f0);
fprintf('最小波长(横波)：%.4f m\n', lambda_min);
fprintf('网格间距：%.4f m\n', dx);
fprintf('每波长网格点数：%.1f个 (推荐>10)\n', lambda_min/dx);
fprintf('CFL数：%.2f (推荐<0.5)\n', cfl);
fprintf('每周期时间步数：%.1f个 (推荐>15)\n', 1/(f0*dt));

% 频散误差估计
dispersion_error = (pi * f0 * dx / vs_min)^2 / 24;  % 基于横波的频散误差估计
fprintf('估计频散误差(横波)：%.2e (推荐<1e-3)\n', dispersion_error);

% 频散误差评估和建议
if dispersion_error <= 1e-3
    fprintf('✓ 频散误差控制良好！\n');
elseif dispersion_error <= 2e-3
    fprintf('⚠ 频散误差稍大但可接受，建议监控结果质量\n');
    fprintf('  优化建议：增加points_per_wavelength到25或降低频率\n');
else
    warning('频散误差较大，建议优化网格参数！');
    fprintf('  建议1：增加points_per_wavelength到25-30\n');
    fprintf('  建议2：降低频率到2000Hz或更低\n');
    fprintf('  建议3：使用四阶差分格式\n');
end

if lambda_min/dx < 10
    warning('每波长网格点数不足，可能产生严重频散！');
end
fprintf('===============================\n');

%% ========================================================================
%  第三部分：偶极子震源设置
%% ========================================================================

% 偶极子震源中心位置
source_x = Lx / 2;
source_y = Ly / 2;
source_z = 1.0;

% 偶极子震源网格索引
isx = round(source_x / dx) + 1;
isy = round(source_y / dy) + 1;
isz = round(source_z / dz) + 1;

% 改进的偶极子震源设计（X方向偶极子）
% 使用应力场加载，而不是速度场，更符合物理实际
dipole_separation = dx;  % 偶极子间距等于网格间距
isx_pos = isx;           % 正极点
isx_neg = isx + 1;       % 负极点（X方向相邻网格）
isy_dipole = isy;        % Y坐标相同
isz_dipole = isz;        % Z坐标相同

% 验证偶极子位置是否在计算域内
if isx_neg > nx || isx_pos < 1 || isy_dipole < 1 || isy_dipole > ny || isz_dipole < 1 || isz_dipole > nz
    error('偶极子震源位置超出计算域范围！');
end

fprintf('\n=== 偶极子震源配置 ===\n');
fprintf('震源类型：X方向偶极子（主要激发横波）\n');
fprintf('中心位置：(%.2f, %.2f, %.2f)m\n', source_x, source_y, source_z);
fprintf('正极点网格索引：(%d, %d, %d)\n', isx_pos, isy_dipole, isz_dipole);
fprintf('负极点网格索引：(%d, %d, %d)\n', isx_neg, isy_dipole, isz_dipole);
fprintf('偶极子间距：%.4f m\n', dipole_separation);
fprintf('激发方式：速度场加载（Vx分量）- 推荐的实用方法\n');
fprintf('优势：直观、高效、工业标准、易于调试\n');

% Ricker子波参数（f0已在上面定义）
t0 = 1.0/f0;

% 震源函数生成
src = zeros(nt,1);

for it = 1:nt
    t = it * dt - t0;
    src(it) = (1 - 2 * pi^2 * f0^2 * t^2) * exp(-pi^2 * f0^2 * t^2);
end

% 震源数据保存
fid = fopen('src.dat','wb'); fwrite(fid,src(:),'double'); fclose(fid);

%% ========================================================================
%  第四部分：检波器设置
%% ========================================================================

% 多检波器阵列参数 - 增加检波器数量
first_receiver_offset = 3.0;  % 第一个检波器在震源上方2米
receiver_spacing = 0.15;      % 检波器间距0.15米

% 计算检波器数量（从第一个检波器开始，每0.15米向上布置到网格顶部）
max_z_position = source_z + first_receiver_offset;
while max_z_position < Lz - 0.3  % 留出边界余量
    max_z_position = max_z_position + receiver_spacing;
end
num_receivers = floor((max_z_position - (source_z + first_receiver_offset)) / receiver_spacing) + 1;
fprintf('检波器数量：%d个\n', num_receivers);

% 检波器位置计算（相对于震源）
receiver_x = repmat(source_x, num_receivers, 1);
receiver_y = repmat(source_y, num_receivers, 1);
receiver_z = zeros(num_receivers, 1);
for i = 1:num_receivers
    receiver_z(i) = source_z + first_receiver_offset + (i-1) * receiver_spacing;
end

% 检波器网格索引
irx = round(receiver_x / dx) + 1;
iry = round(receiver_y / dy) + 1;
irz = round(receiver_z / dz) + 1;

% ========================================================================
% DAS系统参数设置（与检波器位置对应）
% ========================================================================

% DAS系统开关和参数
enable_das = true;                    % DAS系统开关（true=启用，false=禁用）
num_das_points = num_receivers;       % DAS标距点数量，与检波器数量相同
gauge_length = 0.4;                   % 标距长度(米) - 可调整以优化信噪比
gauge_overlap = 0.5;                  % 标距重合比例(0-1之间) - 可调整以优化覆盖

fprintf('\n=== DAS系统配置 ===\n');
if enable_das
    fprintf('DAS系统: 启用\n');
    fprintf('DAS标距点数量: %d\n', num_das_points);
    fprintf('标距长度: %.2f米\n', gauge_length);
    fprintf('标距重合比例: %.1f%%\n', gauge_overlap*100);
    fprintf('DAS与检波器位置完全对应\n');
    fprintf('DAS测量：X方向应变率（适合偶极子横波）\n');
else
    fprintf('DAS系统: 禁用\n');
end
fprintf('========================\n');

% 数据存储初始化（三个方向分量）
receiver_data_x = zeros(nt, num_receivers);     % 检波器X方向速度数据
receiver_data_y = zeros(nt, num_receivers);     % 检波器Y方向速度数据
receiver_data_z = zeros(nt, num_receivers);     % 检波器Z方向速度数据
time_axis = (0:nt-1) * dt;

fprintf('\n=== 多分量数据采集配置 ===\n');
fprintf('检波器记录：X、Y、Z三个方向速度分量\n');
fprintf('X方向：偶极子主要激发方向（横波）\n');
fprintf('Y方向：垂直于偶极子方向（次要横波）\n');
fprintf('Z方向：纵向传播分量\n');

% DAS数据存储初始化（三个方向分量）
if enable_das
    das_data_x = zeros(nt, num_das_points);      % DAS X方向应变率数据
    das_data_y = zeros(nt, num_das_points);      % DAS Y方向应变率数据
    das_data_z = zeros(nt, num_das_points);      % DAS Z方向应变率数据
    das_strain_rate_x = zeros(num_das_points, 1); % X方向应变率缓存
    das_strain_rate_y = zeros(num_das_points, 1); % Y方向应变率缓存
    das_strain_rate_z = zeros(num_das_points, 1); % Z方向应变率缓存
    gauge_centers = zeros(num_das_points, 1);    % 每个标距中心点的Z位置

    % DAS标距中心点位置计算（与检波器位置对应）
    for i = 1:num_das_points
        gauge_centers(i) = receiver_z(i);  % 标距中心对应检波器位置
    end

    fprintf('DAS记录：X、Y、Z三个方向应变率分量\n');
    fprintf('DAS覆盖范围: %.2f米 - %.2f米\n', gauge_centers(1), gauge_centers(end));
end
fprintf('===============================\n');

% 源距计算（用于显示）
source_distances = receiver_z - source_z;

% 检波器位置验证
for i = 1:num_receivers
    if irx(i) < 1 || irx(i) > nx || iry(i) < 1 || iry(i) > ny || irz(i) < 1 || irz(i) > nz
        warning('检波器%d位置超出计算域范围！', i);
        fprintf('检波器%d位置：(%.2f, %.2f, %.2f)m\n', i, receiver_x(i), receiver_y(i), receiver_z(i));
        fprintf('网格索引：(%d, %d, %d)\n', irx(i), iry(i), irz(i));
        fprintf('网格范围：(1-%d, 1-%d, 1-%d)\n', nx, ny, nz);
    end
end

% 实时波形显示已取消，改为进度显示
% 如需实时查看波形，可在计算完成后加载数据进行分析

%% ========================================================================
%  第五部分：边界条件
%% ========================================================================

% PML权重初始化
weights_x = ones(nx+1,1);
weights_y = ones(ny+1,1);
weights_z = ones(nz+1,1);

% PML层参数（优化以减少尾波干扰）
lpml = 20;  % 增加PML层厚度

% PML衰减系数数组
pml_x = zeros(nx, 1);
pml_y = zeros(ny, 1);
pml_z = zeros(nz, 1);
pml_width = lpml;

% PML衰减系数计算（预分配sigma数组避免警告）
sigma = zeros(lpml, 1);  % 预分配数组避免警告

for i = 1:lpml
    R = 1e-9;  % 衰减系数

    vmax = max(sqrt((K + 4/3*G)./rho), [], 'all');

    d_normalized = (lpml - i + 1) / lpml;

    sigma(i) = (3/2) * (vmax/dx) * log(1/R) * d_normalized^2;
    
    pml_x(i) = sigma(i);
    pml_x(nx-i+1) = sigma(i);

    pml_y(i) = sigma(i);
    pml_y(ny-i+1) = sigma(i);

    pml_z(i) = sigma(i);
    pml_z(nz-i+1) = sigma(i);
end

% PML权重计算
for i = 1:lpml
    damping_factor_x = exp(-pml_x(i) * dt);
    weights_x(i) = damping_factor_x;
    
    damping_factor_y = exp(-pml_y(i) * dt);
    weights_y(i) = damping_factor_y;
    
    damping_factor_z = exp(-pml_z(i) * dt);
    weights_z(i) = damping_factor_z;
    
    weights_x(nx-i+1) = damping_factor_x;
    weights_y(ny-i+1) = damping_factor_y;
    weights_z(nz-i+1) = damping_factor_z;
end

% PML权重插值和重塑
weights_xP = 0.5*(weights_x(1:end-1)+weights_x(2:end));
weights_yP = 0.5*(weights_y(1:end-1)+weights_y(2:end));
weights_zP = 0.5*(weights_z(1:end-1)+weights_z(2:end));

weights_yP = reshape(weights_yP,1,ny);
weights_zP = reshape(weights_zP,1,1,nz);
weights_y = reshape(weights_y,1,ny+1);
weights_z = reshape(weights_z,1,1,nz+1);

% 剪切模量插值（交错网格）
G_tau_xy = zeros(nx+1, ny+1, nz);
G_tau_xy(2:end-1,2:end-1,:) = (1/4*(1./G(1:end-1,1:end-1,:) + 1./G(2:end,2:end,:) + 1./G(2:end,1:end-1,:) + 1./G(1:end-1,2:end,:)) ).^(-1);
G_tau_xz = zeros(nx+1, ny, nz+1);
G_tau_xz(2:end-1,:,2:end-1) = (1/4*(1./G(1:end-1,:,1:end-1) + 1./G(2:end,:,2:end) + 1./G(2:end,:,1:end-1) + 1./G(1:end-1,:,2:end)) ).^(-1);
G_tau_yz = zeros(nx, ny+1, nz+1);
G_tau_yz(:,2:end-1,2:end-1) = (1/4*(1./G(:,1:end-1,1:end-1) + 1./G(:,2:end,2:end) + 1./G(:,2:end,1:end-1) + 1./G(:,1:end-1,2:end)) ).^(-1);

% 密度插值（交错网格）
rho_x = zeros(nx+1, ny, nz);
rho_y = zeros(nx, ny+1, nz);
rho_z = zeros(nx, ny, nz+1);

rho_x(2:end-1,:,:) = 0.5 .* (rho(1:end-1,:,:) + rho(2:end,:,:));
rho_x(1,:,:) = rho(1,:,:);
rho_x(end,:,:) = rho(end,:,:);

rho_y(:,2:end-1,:) = 0.5 .* (rho(:,1:end-1,:) + rho(:,2:end,:));
rho_y(:,1,:) = rho(:,1,:);
rho_y(:,end,:) = rho(:,end,:);

rho_z(:,:,2:end-1) = 0.5 .* (rho(:,:,1:end-1) + rho(:,:,2:end));
rho_z(:,:,1) = rho(:,:,1);
rho_z(:,:,end) = rho(:,:,end);

fileList = {'K.dat', 'G.dat', 'rho.dat'};
varList = {K, G, rho};
for i = 1:3
    fid = fopen(fileList{i},'wb'); fwrite(fid, varList{i}(:), 'double'); fclose(fid);
end

parameter_1 = [dx dy dz dt isx isy isz];
fid = fopen('parameter_1.dat','wb'); fwrite(fid, parameter_1, 'double'); fclose(fid);

%% ========================================================================
%  第六部分：场变量初始化
%% ========================================================================

% 速度场初始化（交错网格）
Vx = zeros(nx+1, ny, nz);
Vy = zeros(nx, ny+1, nz);
Vz = zeros(nx, ny, nz+1);

% 正应力场初始化
tau_xx = zeros(nx, ny, nz);
tau_yy = zeros(nx, ny, nz);
tau_zz = zeros(nx, ny, nz);

% 剪切应力场初始化（交错网格）
tau_xy = zeros(nx+1, ny+1, nz);
tau_xz = zeros(nx+1, ny, nz+1);
tau_yz = zeros(nx, ny+1, nz+1);

% 压力场初始化
Pr = zeros(nx, ny, nz);


%% ========================================================================
%  第七部分：时间循环（优化的FDTD算法）
%% ========================================================================

fprintf('\n=== 开始FDTD时间循环 ===\n');
fprintf('总时间步数：%d\n', nt);
fprintf('进度显示：每%d步更新一次\n', PROGRESS_INTERVAL);

% 进度显示参数
progress_interval = PROGRESS_INTERVAL;  % 从快速设置区域获取进度显示间隔
start_time = tic;  % 记录开始时间

for it = 1:nt

    % 偶极子震源加载（您原来的方式是正确的！）
    % X方向偶极子：在Vx速度分量上加载反向激发
    % 注释：速度源和应力源在FDTD中都是有效的，您的原始方法没有问题
    if isx_pos >= 1 && isx_pos <= nx && isy_dipole >= 1 && isy_dipole <= ny && isz_dipole >= 1 && isz_dipole <= nz
        Vx(isx_pos, isy_dipole, isz_dipole) = Vx(isx_pos, isy_dipole, isz_dipole) + src(it);   % 正极点
    end
    if isx_neg >= 1 && isx_neg <= nx && isy_dipole >= 1 && isy_dipole <= ny && isz_dipole >= 1 && isz_dipole <= nz
        Vx(isx_neg, isy_dipole, isz_dipole) = Vx(isx_neg, isy_dipole, isz_dipole) - src(it);   % 负极点
    end

    % 检波器数据采集（记录X、Y、Z三个方向速度分量）
    for i = 1:num_receivers
        if irx(i) >= 1 && irx(i) <= nx && iry(i) >= 1 && iry(i) <= ny && irz(i) >= 1 && irz(i) <= nz
            % 记录三个方向的速度分量
            receiver_data_x(it, i) = Vx(irx(i), iry(i), irz(i));  % X方向（偶极子主要激发）
            receiver_data_y(it, i) = Vy(irx(i), iry(i), irz(i));  % Y方向（垂直分量）
            receiver_data_z(it, i) = Vz(irx(i), iry(i), irz(i));  % Z方向（纵向分量）
        end
    end

    % ========================================================================
    % DAS应变率数据采集（仅在启用DAS时执行）
    % ========================================================================
    if enable_das
        % 说明：DAS通过测量光纤沿线的应变率变化来检测声波信号
        % 对于偶极子震源，需要测量X、Y、Z三个方向的应变率变化
        for i = 1:num_das_points
            % 计算当前标距点对应的检波点位置（Z方向）
            rec_z_pos = irz(i);  % 检波器的Z方向网格索引

            % 计算标距的两个端点位置（沿Z方向）
            gauge_half_length_grid = round(gauge_length/(2*dz));  % 标距半长度（网格点数）
            gauge_start = max(1, rec_z_pos - gauge_half_length_grid);     % 标距起始点
            gauge_end = min(nz, rec_z_pos + gauge_half_length_grid);      % 标距结束点

            % DAS测量三个方向的应变率
            if gauge_start >= 1 && gauge_end <= nz && irx(i) >= 1 && irx(i) <= nx && iry(i) >= 1 && iry(i) <= ny
                % X方向应变率（偶极子主要激发方向）
                vx_start = Vx(irx(i), iry(i), gauge_start);
                vx_end = Vx(irx(i), iry(i), gauge_end);

                % Y方向应变率（垂直于偶极子方向）
                vy_start = Vy(irx(i), iry(i), gauge_start);
                vy_end = Vy(irx(i), iry(i), gauge_end);

                % Z方向应变率（纵向传播方向）
                vz_start = Vz(irx(i), iry(i), gauge_start);
                vz_end = Vz(irx(i), iry(i), gauge_end);

                % 计算实际标距长度并计算应变率
                actual_distance = (gauge_end - gauge_start) * dz;  % 实际标距长度（米）
                if actual_distance > 0
                    das_strain_rate_x(i) = (vx_end - vx_start) / actual_distance;
                    das_strain_rate_y(i) = (vy_end - vy_start) / actual_distance;
                    das_strain_rate_z(i) = (vz_end - vz_start) / actual_distance;
                else
                    das_strain_rate_x(i) = 0;  % 避免除零错误
                    das_strain_rate_y(i) = 0;
                    das_strain_rate_z(i) = 0;
                end
            else
                das_strain_rate_x(i) = 0;  % 边界外设为0
                das_strain_rate_y(i) = 0;
                das_strain_rate_z(i) = 0;
            end
        end

        % 存储DAS数据（三个方向）
        das_data_x(it, :) = das_strain_rate_x';
        das_data_y(it, :) = das_strain_rate_y';
        das_data_z(it, :) = das_strain_rate_z';
    end
    
    % 进度显示（每指定步数显示一次）
    if mod(it, progress_interval) == 0 || it == nt
        elapsed_time = toc(start_time);
        progress_percent = 100 * it / nt;

        if it < nt
            estimated_total_time = elapsed_time * nt / it;
            remaining_time = estimated_total_time - elapsed_time;
            fprintf('进度: %.1f%% (%d/%d) | 已用时: %.1fs | 预计剩余: %.1fs\n', ...
                    progress_percent, it, nt, elapsed_time, remaining_time);
        else
            fprintf('进度: 100.0%% (%d/%d) | 总用时: %.1fs | 计算完成!\n', ...
                    it, nt, elapsed_time);
        end
    end
    
    % 应力场更新（FDTD算法）
    divV = diff(Vx,1,1)/dx + diff(Vy,1,2)/dy + diff(Vz,1,3)/dz;
    
    Pr = (Pr - K.*divV*dt);
    
    tau_xx = (tau_xx + 2*G.*dt.*( diff(Vx,1,1)/dx - 1/3*divV ));
    tau_yy = (tau_yy + 2*G.*dt.*( diff(Vy,1,2)/dy - 1/3*divV ));
    tau_zz = (tau_zz + 2*G.*dt.*( diff(Vz,1,3)/dz - 1/3*divV ));
    
    tau_xy(2:end-1,2:end-1,:) = (tau_xy(2:end-1,2:end-1,:) + G_tau_xy(2:end-1,2:end-1,:).*dt.*( diff(Vy(:, 2:end-1, :),1,1)/dx + diff(Vx(2:end-1, :, :),1,2)/dy ));
    tau_xz(2:end-1,:,2:end-1) = (tau_xz(2:end-1,:,2:end-1) + G_tau_xz(2:end-1,:,2:end-1).*dt.*( diff(Vz(:, :, 2:end-1),1,1)/dx + diff(Vx(2:end-1, :, :),1,3)/dz ));
    tau_yz(:,2:end-1,2:end-1) = (tau_yz(:,2:end-1,2:end-1) + G_tau_yz(:,2:end-1,2:end-1).*dt.*( diff(Vy(:, 2:end-1, :),1,3)/dz + diff(Vz(:, :, 2:end-1),1,2)/dy ));
    
    % PML边界条件应用（z方向）
    for i = 1:nx
        for j=1:ny
            Pr(i,j,:) = Pr(i,j,:) .*weights_zP;
            tau_xx(i,j,:) = tau_xx(i,j,:) .*weights_zP;
            tau_yy(i,j,:) = tau_yy(i,j,:) .*weights_zP;
            tau_zz(i,j,:) = tau_zz(i,j,:) .*weights_zP;
            tau_xy(i,j,:) = tau_xy(i,j,:) .*weights_zP;
            tau_xz(i,j,2:end-1) = tau_xz(i,j,2:end-1) .*weights_zP(2:end);
            tau_yz(i,j,2:end-1) = tau_yz(i,j,2:end-1) .*weights_zP(2:end);
        end
    end
    
    % PML边界条件应用（y方向）
    for i = 1:nx
        for k = 1:nz
            Pr(i,:,k) = Pr(i,:,k) .*weights_yP;
            tau_xx(i,:,k) = tau_xx(i,:,k) .*weights_yP;
            tau_yy(i,:,k) = tau_yy(i,:,k) .*weights_yP;
            tau_zz(i,:,k) = tau_zz(i,:,k) .*weights_yP;
            tau_xy(i,2:end-1,k) = tau_xy(i,2:end-1,k) .*weights_yP(2:end);
            tau_xz(i,:,k) = tau_xz(i,:,k) .*weights_yP;
            tau_yz(i,2:end-1,k) = tau_yz(i,2:end-1,k) .*weights_yP(2:end);
        end
    end
    
    % PML边界条件应用（x方向）
    for j = 1:ny
        for k = 1:nz
            Pr(:,j,k) = Pr(:,j,k) .*weights_xP;
            tau_xx(:,j,k) = tau_xx(:,j,k) .*weights_xP;
            tau_yy(:,j,k) = tau_yy(:,j,k) .*weights_xP;
            tau_zz(:,j,k) = tau_zz(:,j,k) .*weights_xP;
            tau_xy(2:end-1,j,k) = tau_xy(2:end-1,j,k) .*weights_xP(2:end);
            tau_xz(2:end-1,j,k) = tau_xz(2:end-1,j,k) .*weights_xP(2:end);
            tau_yz(:,j,k) = tau_yz(:,j,k) .*weights_xP;
        end
    end

    % 速度场更新（FDTD算法）
    Vx(2:end-1,2:end-1,2:end-1) = Vx(2:end-1,2:end-1,2:end-1) + dt./rho_x(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xx(:,2:end-1,2:end-1) - Pr(:,2:end-1,2:end-1),1,1)/dx + diff(tau_xy(2:end-1,2:end-1,2:end-1),1,2)/dy + diff(tau_xz(2:end-1,2:end-1,2:end-1),1,3)/dz );
    
    Vy(2:end-1,2:end-1,2:end-1) = Vy(2:end-1,2:end-1,2:end-1) + dt./rho_y(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xy(2:end-1,2:end-1,2:end-1),1,1)/dx + diff(tau_yy(2:end-1,:,2:end-1) - Pr(2:end-1,:,2:end-1),1,2)/dy + diff(tau_yz(2:end-1,2:end-1,2:end-1),1,3)/dz );
    
    Vz(2:end-1,2:end-1,2:end-1) = Vz(2:end-1,2:end-1,2:end-1) + dt./rho_z(2:end-1,2:end-1,2:end-1) .* ( diff(tau_xz(2:end-1,2:end-1,2:end-1),1,1)/dx + diff(tau_yz(2:end-1,2:end-1,2:end-1),1,2)/dy + diff(tau_zz(2:end-1,2:end-1,:) - Pr(2:end-1,2:end-1,:),1,3)/dz );
    
    % PML边界条件应用于速度场
    for j = 1:ny
        for k = 1:nz
            Vx(2:end-1,j,k) = Vx(2:end-1,j,k) .*weights_x(2:end-1);
        end
    end
    
    for i = 1:nx
        for k = 1:nz
            Vy(i,2:end-1,k) = Vy(i,2:end-1,k) .*weights_y(2:end-1);
        end
    end
    
    for i = 1:nx
        for j = 1:ny
            Vz(i,j,2:end-1) = Vz(i,j,2:end-1) .*weights_z(2:end-1);
        end
    end
    
end  % 主时间循环结束

%% ========================================================================
%  第八部分：结果处理
%% ========================================================================

% 模拟完成提示
fprintf('\n=== 偶极子FDTD模拟完成 ===\n');

% 数值频散质量评估
fprintf('\n=== 数值频散质量评估 ===\n');
fprintf('使用的网格参数:\n');
fprintf('  - 每波长网格点数(横波): %.1f个\n', lambda_min/dx);
fprintf('  - CFL数: %.2f\n', cfl);
fprintf('  - 每周期时间步数: %.1f个\n', 1/(f0*dt));
fprintf('  - 估计频散误差(横波): %.2e\n', dispersion_error);

% 简单的频散检查（比较理论和数值传播时间）
if num_receivers >= 2
    % 计算第一个和最后一个检波器之间的理论传播时间
    distance = source_distances(end) - source_distances(1);
    theoretical_time_s = distance / vs_rock;  % 横波传播时间

    fprintf('\n检波器间距离: %.2f m\n', distance);
    fprintf('理论横波传播时间: %.2f μs\n', theoretical_time_s*1e6);
    fprintf('如果数值结果与理论值差异较大，可能存在频散问题\n');
end

% ========================================================================
% 数据分析和保存
% ========================================================================

% 波形特征分析（三个方向分量）
max_amplitudes_receiver_x = max(abs(receiver_data_x), [], 1);
max_amplitudes_receiver_y = max(abs(receiver_data_y), [], 1);
max_amplitudes_receiver_z = max(abs(receiver_data_z), [], 1);

% DAS数据分析（如果启用）
if enable_das
    max_amplitudes_das_x = max(abs(das_data_x), [], 1);
    max_amplitudes_das_y = max(abs(das_data_y), [], 1);
    max_amplitudes_das_z = max(abs(das_data_z), [], 1);

    fprintf('\n=== 多分量DAS与检波器数据对比 ===\n');
    fprintf('检波器最大振幅范围:\n');
    fprintf('  X方向: %.2e - %.2e (偶极子主要激发)\n', min(max_amplitudes_receiver_x), max(max_amplitudes_receiver_x));
    fprintf('  Y方向: %.2e - %.2e (垂直分量)\n', min(max_amplitudes_receiver_y), max(max_amplitudes_receiver_y));
    fprintf('  Z方向: %.2e - %.2e (纵向分量)\n', min(max_amplitudes_receiver_z), max(max_amplitudes_receiver_z));

    fprintf('DAS最大应变率范围:\n');
    fprintf('  X方向: %.2e - %.2e (偶极子主要激发)\n', min(max_amplitudes_das_x), max(max_amplitudes_das_x));
    fprintf('  Y方向: %.2e - %.2e (垂直分量)\n', min(max_amplitudes_das_y), max(max_amplitudes_das_y));
    fprintf('  Z方向: %.2e - %.2e (纵向分量)\n', min(max_amplitudes_das_z), max(max_amplitudes_das_z));

    % 简单的信噪比分析
    receiver_snr_x = max(max_amplitudes_receiver_x) / (mean(max_amplitudes_receiver_x) + eps);
    receiver_snr_y = max(max_amplitudes_receiver_y) / (mean(max_amplitudes_receiver_y) + eps);
    receiver_snr_z = max(max_amplitudes_receiver_z) / (mean(max_amplitudes_receiver_z) + eps);

    das_snr_x = max(max_amplitudes_das_x) / (mean(max_amplitudes_das_x) + eps);
    das_snr_y = max(max_amplitudes_das_y) / (mean(max_amplitudes_das_y) + eps);
    das_snr_z = max(max_amplitudes_das_z) / (mean(max_amplitudes_das_z) + eps);

    fprintf('检波器信噪比估计: X=%.1f, Y=%.1f, Z=%.1f\n', receiver_snr_x, receiver_snr_y, receiver_snr_z);
    fprintf('DAS信噪比估计: X=%.1f, Y=%.1f, Z=%.1f\n', das_snr_x, das_snr_y, das_snr_z);
end

% 数据保存（包含检波器和DAS数据）
save_filename = sprintf('dipole_acoustic_logging_3D_%dreceiver_with_DAS_results.mat', num_receivers);

if enable_das
    % 保存包含DAS数据的完整数据集
    save(save_filename, ...
         'receiver_data', 'das_data', 'src', 'time_axis', ...
         'receiver_x', 'receiver_y', 'receiver_z', ...
         'source_x', 'source_y', 'source_z', ...
         'isx_pos', 'isx_neg', 'isy_dipole', 'isz_dipole', ...
         'source_distances', 'num_receivers', ...
         'first_receiver_offset', 'receiver_spacing', ...
         'max_amplitudes_receiver', 'max_amplitudes_das', ...
         'enable_das', 'num_das_points', 'gauge_length', 'gauge_overlap', ...
         'gauge_centers', 'das_strain_rate', ...
         'dt', 'dx', 'dy', 'dz', 'nt', 'nx', 'ny', 'nz', ...
         'vp_fluid', 'vs_fluid', 'rho_fluid', ...
         'vp_rock', 'vs_rock', 'rho_rock', ...
         'well_radius', 'f0', 't0', ...
         'lambda_min', 'points_per_wavelength', 'cfl', 'dispersion_error');
else
    % 只保存检波器数据
    save(save_filename, ...
         'receiver_data', 'src', 'time_axis', ...
         'receiver_x', 'receiver_y', 'receiver_z', ...
         'source_x', 'source_y', 'source_z', ...
         'isx_pos', 'isx_neg', 'isy_dipole', 'isz_dipole', ...
         'source_distances', 'num_receivers', ...
         'first_receiver_offset', 'receiver_spacing', ...
         'max_amplitudes_receiver', ...
         'enable_das', ...
         'dt', 'dx', 'dy', 'dz', 'nt', 'nx', 'ny', 'nz', ...
         'vp_fluid', 'vs_fluid', 'rho_fluid', ...
         'vp_rock', 'vs_rock', 'rho_rock', ...
         'well_radius', 'f0', 't0', ...
         'lambda_min', 'points_per_wavelength', 'cfl', 'dispersion_error');
end

fprintf('\n=== 数据保存完成 ===\n');
fprintf('数据已保存到：%s\n', save_filename);
fprintf('包含%d个检波器的X方向速度分量波形数据（偶极子横波信号）。\n', num_receivers);
if enable_das
    fprintf('同时包含%d个DAS标距点的X方向应变率数据。\n', num_das_points);
    fprintf('DAS参数：标距长度%.2fm，重合比例%.1f%%\n', gauge_length, gauge_overlap*100);
end
fprintf('偶极子震源配置：正负极点间距%.3fm，检波器源距%.2fm，间距%.2fm\n', dipole_separation, first_receiver_offset, receiver_spacing);

% 数据调用示例
fprintf('\n=== 数据调用示例 ===\n');
fprintf('load(''%s'');  %% 加载数据\n', save_filename);
fprintf('%% 检波器数据(X方向速度-横波信号):\n');
fprintf('figure; imagesc(time_axis*1e6, 1:num_receivers, receiver_data'');  %% 显示检波器数据\n');
fprintf('xlabel(''时间 (μs)''); ylabel(''检波器道数''); title(''偶极子检波器波形数据(横波)'');\n');
if enable_das
    fprintf('%% DAS数据(X方向应变率):\n');
    fprintf('figure; imagesc(time_axis*1e6, 1:num_das_points, das_data'');  %% 显示DAS数据\n');
    fprintf('xlabel(''时间 (μs)''); ylabel(''DAS标距点''); title(''DAS X方向应变率数据'');\n');
    fprintf('%% 对比分析:\n');
    fprintf('figure; subplot(2,1,1); plot(time_axis*1e6, receiver_data(:,2)); title(''第2个检波器(横波)'');\n');
    fprintf('subplot(2,1,2); plot(time_axis*1e6, das_data(:,2)); title(''第2个DAS标距点'');\n');
end
fprintf('========================\n');

%% ========================================================================
%  程序结束
%% ========================================================================